<!DOCTYPE html>
<html>
<head>
  <base href="$FLUTTER_BASE_HREF">

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="A new Flutter project.">

  <!-- iOS meta tags & icons -->
  <meta name="mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="flame_game">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png"/>

  <title>flame_game</title>
  <link rel="manifest" href="manifest.json">
</head>
<body>
  <div id="loading">Loading...</div>
  <script>
    window.addEventListener('load', function(ev) {
      console.log('Page loaded, initializing Flutter...');
      
      _flutter.loader.loadEntrypoint({
        serviceWorker: {
          serviceWorkerVersion: null,
        },
        onEntrypointLoaded: function(engineInitializer) {
          console.log('Entrypoint loaded, initializing engine...');
          
          engineInitializer.initializeEngine({
            renderer: "html"
          }).then(function(appRunner) {
            console.log('Engine initialized, running app...');
            document.getElementById('loading').style.display = 'none';
            appRunner.runApp();
          }).catch(function(error) {
            console.error('Engine initialization failed:', error);
          });
        }
      });
    });
  </script>
  <script src="flutter.js" defer></script>
</body>
</html>
