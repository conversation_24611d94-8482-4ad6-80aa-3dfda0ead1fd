// pubspec.yaml dependencies needed:
// dependencies:
//   flutter:
//     sdk: flutter
//   flame: ^1.10.1

import 'package:flutter/material.dart';
import 'package:flame/game.dart';
import 'package:flame/components.dart';
import 'package:flame/events.dart';
import 'package:flame/collisions.dart';
import 'dart:math';

void main() {
  runApp(GameApp());
}

class GameApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Breakout Game',
      home: GameWidget<BreakoutGame>.controlled(
        gameFactory: BreakoutGame.new,
      ),
      debugShowCheckedModeBanner: false,
    );
  }
}

enum GameState {
  menu,
  playing,
  gameOver,
  win,
  levelSelect,
}

class Level {
  final int number;
  final String name;
  final int rows;
  final int cols;
  final Color brickColor;
  final double ballSpeed;

  Level({
    required this.number,
    required this.name,
    required this.rows,
    required this.cols,
    required this.brickColor,
    required this.ballSpeed,
  });
}

class GameLevels {
  static final List<Level> levels = [
    Level(
      number: 1,
      name: "Easy Start",
      rows: 3,
      cols: 6,
      brickColor: Colors.orange,
      ballSpeed: 200,
    ),
    Level(
      number: 2,
      name: "Getting Harder",
      rows: 4,
      cols: 7,
      brickColor: Colors.red,
      ballSpeed: 250,
    ),
    Level(
      number: 3,
      name: "Challenge Mode",
      rows: 5,
      cols: 8,
      brickColor: Colors.purple,
      ballSpeed: 300,
    ),
  ];
}

class BreakoutGame extends FlameGame with TapDetector, HasCollisionDetection {
  late Ball ball;
  late Paddle paddle;
  late TextComponent scoreText;
  late TextComponent gameOverText;
  late TextComponent winText;
  late TextComponent levelText;
  late RectangleComponent background;

  // Menu components
  late TextComponent titleText;
  late MenuButton levelSelectButton;
  late MenuButton exitButton;
  late MenuButton backButton;

  // Level select components
  List<LevelButton> levelButtons = [];

  GameState currentState = GameState.menu;
  int currentLevel = 0;
  int score = 0;
  final Random random = Random();
  List<Brick> bricks = [];

  @override
  Future<void> onLoad() async {
    // Add background
    background = RectangleComponent(
      size: size,
      paint: Paint()..color = const Color(0xFF001122),
    );
    add(background);

    // Initialize all UI components
    await _initializeMenuComponents();
    await _initializeGameComponents();
    await _initializeLevelSelectComponents();

    // Start with menu
    _showMenu();
  }

  Future<void> _initializeMenuComponents() async {
    // Title
    titleText = TextComponent(
      text: 'BREAKOUT GAME',
      position: Vector2(size.x / 2, size.y * 0.25),
      anchor: Anchor.center,
      textRenderer: TextPaint(
        style: const TextStyle(
          fontSize: 48,
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
    add(titleText);

    // Level select button
    levelSelectButton = MenuButton(
      text: 'SELECT LEVEL',
      position: Vector2(size.x / 2, size.y * 0.45),
      onPressed: () => _showLevelSelect(),
    );
    add(levelSelectButton);

    // Exit button
    exitButton = MenuButton(
      text: 'EXIT GAME',
      position: Vector2(size.x / 2, size.y * 0.55),
      onPressed: () => _exitGame(),
    );
    add(exitButton);
  }

  Future<void> _initializeGameComponents() async {
    // Add score text
    scoreText = TextComponent(
      text: 'Score: 0',
      position: Vector2(20, 50),
      textRenderer: TextPaint(
        style: const TextStyle(
          fontSize: 24,
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
    scoreText.scale = Vector2.zero(); // Initially hidden
    add(scoreText);

    // Add level text
    levelText = TextComponent(
      text: 'Level: 1',
      position: Vector2(size.x - 150, 50),
      textRenderer: TextPaint(
        style: const TextStyle(
          fontSize: 24,
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
    levelText.scale = Vector2.zero(); // Initially hidden
    add(levelText);

    // Add game over text (initially hidden)
    gameOverText = TextComponent(
      text: 'Game Over!\nTap to Restart',
      position: Vector2(size.x / 2, size.y / 2),
      anchor: Anchor.center,
      textRenderer: TextPaint(
        style: const TextStyle(
          fontSize: 32,
          color: Colors.red,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
    gameOverText.scale = Vector2.zero(); // Initially hidden
    add(gameOverText);

    // Add win text (initially hidden)
    winText = TextComponent(
      text: 'Level Complete!\nTap to Continue',
      position: Vector2(size.x / 2, size.y / 2),
      anchor: Anchor.center,
      textRenderer: TextPaint(
        style: const TextStyle(
          fontSize: 32,
          color: Colors.green,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
    winText.scale = Vector2.zero(); // Initially hidden
    add(winText);
  }

  Future<void> _initializeLevelSelectComponents() async {
    // Back button
    backButton = MenuButton(
      text: 'BACK TO MENU',
      position: Vector2(size.x / 2, size.y * 0.75),
      onPressed: () => _showMenu(),
    );
    backButton.scale = Vector2.zero(); // Initially hidden
    add(backButton);

    // Create level buttons
    for (int i = 0; i < GameLevels.levels.length; i++) {
      final level = GameLevels.levels[i];
      final button = LevelButton(
        level: level,
        position: Vector2(size.x / 2, size.y * (0.25 + i * 0.15)),
        onPressed: () => _startLevel(i),
      );
      button.scale = Vector2.zero(); // Initially hidden
      levelButtons.add(button);
      add(button);
    }
  }

  void _showMenu() {
    currentState = GameState.menu;
    _hideAllComponents();
    titleText.scale = Vector2.all(1);
    levelSelectButton.scale = Vector2.all(1);
    exitButton.scale = Vector2.all(1);
  }

  void _showLevelSelect() {
    currentState = GameState.levelSelect;
    _hideAllComponents();
    backButton.scale = Vector2.all(1);
    for (final button in levelButtons) {
      button.scale = Vector2.all(1);
    }
  }

  void _startLevel(int levelIndex) {
    currentLevel = levelIndex;
    currentState = GameState.playing;
    score = 0;

    _hideAllComponents();
    _showGameComponents();
    _setupLevel();
  }

  void _showGameComponents() {
    scoreText.scale = Vector2.all(1);
    levelText.scale = Vector2.all(1);
    levelText.text = 'Level: ${currentLevel + 1}';
  }

  void _hideAllComponents() {
    // Hide menu components
    titleText.scale = Vector2.zero();
    levelSelectButton.scale = Vector2.zero();
    exitButton.scale = Vector2.zero();

    // Hide level select components
    backButton.scale = Vector2.zero();
    for (final button in levelButtons) {
      button.scale = Vector2.zero();
    }

    // Hide game components
    scoreText.scale = Vector2.zero();
    levelText.scale = Vector2.zero();
    gameOverText.scale = Vector2.zero();
    winText.scale = Vector2.zero();
  }

  void _setupLevel() {
    // Remove existing game objects
    try {
      if (children.contains(ball)) ball.removeFromParent();
    } catch (e) {
      // Ball not initialized yet
    }
    try {
      if (children.contains(paddle)) paddle.removeFromParent();
    } catch (e) {
      // Paddle not initialized yet
    }

    for (final brick in bricks) {
      brick.removeFromParent();
    }
    bricks.clear();

    // Create new game objects
    final level = GameLevels.levels[currentLevel];

    // Create bricks based on level
    createBricks(level);

    // Add ball
    ball = Ball(speed: level.ballSpeed);
    add(ball);

    // Add paddle
    paddle = Paddle();
    add(paddle);

    // Update score display
    scoreText.text = 'Score: $score';
  }

  void _exitGame() {
    // In a real app, you might want to show a confirmation dialog
    // For now, we'll just go back to menu
    _showMenu();
  }

  void createBricks(Level level) {
    const spacing = 60.0;
    const startY = 100.0;
    final startX = (size.x - (level.cols - 1) * spacing) / 2;

    for (int row = 0; row < level.rows; row++) {
      for (int col = 0; col < level.cols; col++) {
        final brick = Brick(brickColor: level.brickColor);
        brick.position = Vector2(
          startX + col * spacing,
          startY + row * spacing,
        );
        bricks.add(brick);
        add(brick);
      }
    }
  }

  void breakBrick(Brick brick) {
    brick.removeFromParent();
    bricks.remove(brick);
    score += 10;
    scoreText.text = 'Score: $score';

    // Check win condition
    if (bricks.isEmpty) {
      _levelComplete();
    }
  }

  void _levelComplete() {
    currentState = GameState.win;
    winText.scale = Vector2.all(1);

    // Update win text based on whether there are more levels
    if (currentLevel < GameLevels.levels.length - 1) {
      winText.text = 'Level Complete!\nTap to Continue';
    } else {
      winText.text = 'All Levels Complete!\nTap to Return to Menu';
    }
  }

  void gameOver() {
    currentState = GameState.gameOver;
    gameOverText.scale = Vector2.all(1);
  }

  void _nextLevel() {
    if (currentLevel < GameLevels.levels.length - 1) {
      _startLevel(currentLevel + 1);
    } else {
      // All levels completed, return to menu
      _showMenu();
    }
  }

  void _restartCurrentLevel() {
    _startLevel(currentLevel);
  }

  @override
  bool onTapDown(TapDownInfo info) {
    switch (currentState) {
      case GameState.menu:
        // Handle menu button taps
        break;
      case GameState.levelSelect:
        // Handle level select button taps
        break;
      case GameState.playing:
        // Move paddle towards tap
        try {
          final tapX = info.eventPosition.global.x;
          paddle.moveTo(tapX);
        } catch (e) {
          // Paddle not initialized yet
        }
        break;
      case GameState.gameOver:
        _restartCurrentLevel();
        break;
      case GameState.win:
        _nextLevel();
        break;
    }
    return true;
  }
}

class Ball extends RectangleComponent with HasGameReference<BreakoutGame>, CollisionCallbacks {
  Vector2 velocity;
  final double speed;

  Ball({this.speed = 200}) : velocity = Vector2(speed, -speed);

  @override
  Future<void> onLoad() async {
    size = Vector2.all(20);
    anchor = Anchor.center;
    position = Vector2(game.size.x / 2, game.size.y / 2);
    paint = Paint()..color = Colors.white;

    add(RectangleHitbox());
  }

  @override
  void render(Canvas canvas) {
    // Draw as circle
    canvas.drawCircle(
      Offset(size.x / 2, size.y / 2),
      size.x / 2,
      paint,
    );
  }

  @override
  void update(double dt) {
    super.update(dt);

    if (game.currentState != GameState.playing) return;

    position += velocity * dt;

    // Bounce off walls
    if (position.x <= size.x / 2 || position.x >= game.size.x - size.x / 2) {
      velocity.x = -velocity.x;
      position.x = position.x.clamp(size.x / 2, game.size.x - size.x / 2);
    }

    if (position.y <= size.y / 2) {
      velocity.y = -velocity.y;
      position.y = size.y / 2;
    }

    // Game over if ball goes below paddle
    if (position.y > game.size.y) {
      game.gameOver();
    }
  }

  @override
  bool onCollisionStart(Set<Vector2> intersectionPoints, PositionComponent other) {
    super.onCollisionStart(intersectionPoints, other);

    if (other is Brick) {
      velocity.y = -velocity.y;
      game.breakBrick(other);
      return false;
    } else if (other is Paddle) {
      // Calculate bounce angle based on where ball hits paddle
      final paddleCenter = other.position.x + other.size.x / 2;
      final hitOffset = (position.x - paddleCenter) / (other.size.x / 2);

      velocity.y = -velocity.y.abs(); // Always bounce up
      velocity.x = hitOffset * 300; // Vary horizontal speed based on hit position

      return false;
    }
    return true;
  }
}

class Paddle extends RectangleComponent with HasGameReference<BreakoutGame> {
  double targetX = 0;
  double moveSpeed = 500;

  @override
  Future<void> onLoad() async {
    size = Vector2(100, 15);
    anchor = Anchor.topLeft;
    position = Vector2(game.size.x / 2 - size.x / 2, game.size.y - 50);
    paint = Paint()..color = Colors.blue;

    add(RectangleHitbox());
    targetX = position.x;
  }

  @override
  void update(double dt) {
    super.update(dt);

    // Move towards target X position
    final diff = targetX - position.x;
    if (diff.abs() > 5) {
      final moveX = moveSpeed * dt * (diff > 0 ? 1 : -1);
      position.x += moveX;

      // Keep paddle within screen bounds
      position.x = position.x.clamp(0, game.size.x - size.x);
    }
  }

  void moveTo(double x) {
    targetX = (x - size.x / 2).clamp(0, game.size.x - size.x);
  }
}

class Brick extends RectangleComponent with HasGameReference<BreakoutGame> {
  final Color brickColor;

  Brick({this.brickColor = Colors.orange});

  @override
  Future<void> onLoad() async {
    size = Vector2.all(40);
    anchor = Anchor.center;
    paint = Paint()..color = brickColor;

    add(RectangleHitbox());
  }

  @override
  void render(Canvas canvas) {
    // Draw as circle
    canvas.drawCircle(
      Offset(size.x / 2, size.y / 2),
      size.x / 2,
      paint,
    );
  }
}

// Menu Button Component
class MenuButton extends RectangleComponent with TapCallbacks {
  final String text;
  final VoidCallback onPressed;
  late TextComponent textComponent;

  MenuButton({
    required this.text,
    required Vector2 position,
    required this.onPressed,
  }) {
    this.position = position;
  }

  @override
  Future<void> onLoad() async {
    size = Vector2(200, 50);
    anchor = Anchor.center;
    paint = Paint()
      ..color = Colors.blue.withValues(alpha: 0.8)
      ..style = PaintingStyle.fill;

    // Add text
    textComponent = TextComponent(
      text: text,
      anchor: Anchor.center,
      position: size / 2,
      textRenderer: TextPaint(
        style: const TextStyle(
          fontSize: 18,
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
    add(textComponent);
  }

  @override
  void render(Canvas canvas) {
    // Draw rounded rectangle
    final rect = RRect.fromRectAndRadius(
      Rect.fromLTWH(0, 0, size.x, size.y),
      const Radius.circular(10),
    );
    canvas.drawRRect(rect, paint);
  }

  @override
  bool onTapDown(TapDownEvent event) {
    // Only respond to taps when button is visible
    if (scale.x > 0) {
      onPressed();
      return true;
    }
    return false;
  }
}

// Level Button Component
class LevelButton extends RectangleComponent with TapCallbacks, HasGameReference<BreakoutGame> {
  final Level level;
  final VoidCallback onPressed;
  late TextComponent nameComponent;
  late TextComponent detailsComponent;

  LevelButton({
    required this.level,
    required Vector2 position,
    required this.onPressed,
  }) {
    this.position = position;
  }

  @override
  Future<void> onLoad() async {
    size = Vector2(300, 60);
    anchor = Anchor.center;
    paint = Paint()
      ..color = level.brickColor.withValues(alpha: 0.8)
      ..style = PaintingStyle.fill;

    // Add level name
    nameComponent = TextComponent(
      text: 'Level ${level.number}: ${level.name}',
      anchor: Anchor.centerLeft,
      position: Vector2(20, size.y / 2 - 10),
      textRenderer: TextPaint(
        style: const TextStyle(
          fontSize: 20,
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
    add(nameComponent);

    // Add level details
    detailsComponent = TextComponent(
      text: '${level.rows}x${level.cols} bricks',
      anchor: Anchor.centerLeft,
      position: Vector2(20, size.y / 2 + 10),
      textRenderer: TextPaint(
        style: const TextStyle(
          fontSize: 14,
          color: Colors.white70,
        ),
      ),
    );
    add(detailsComponent);
  }

  @override
  void render(Canvas canvas) {
    // Draw rounded rectangle
    final rect = RRect.fromRectAndRadius(
      Rect.fromLTWH(0, 0, size.x, size.y),
      const Radius.circular(10),
    );
    canvas.drawRRect(rect, paint);
  }

  @override
  bool onTapDown(TapDownEvent event) {
    // Only respond to taps when in level select state and button is visible
    if (game.currentState == GameState.levelSelect && scale.x > 0) {
      onPressed();
      return true;
    }
    return false;
  }
}
