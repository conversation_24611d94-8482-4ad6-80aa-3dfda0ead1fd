// pubspec.yaml dependencies needed:
// dependencies:
//   flutter:
//     sdk: flutter
//   flame: ^1.10.1

import 'package:flutter/material.dart';
import 'package:flame/game.dart';
import 'package:flame/components.dart';
import 'package:flame/events.dart';
import 'package:flame/collisions.dart';
import 'dart:math';

void main() {
  runApp(GameApp());
}

class GameApp extends StatelessWidget {
  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Breakout Game',
      home: GameWidget<BreakoutGame>.controlled(
        gameFactory: BreakoutGame.new,
      ),
      debugShowCheckedModeBanner: false,
    );
  }
}

class BreakoutGame extends FlameGame with TapDetector, HasCollisionDetection {
  late Ball ball;
  late Paddle paddle;
  late TextComponent scoreText;
  late TextComponent gameOverText;
  late TextComponent winText;
  int score = 0;
  bool isGameOver = false;
  bool isWin = false;
  final Random random = Random();
  List<Brick> bricks = [];

  @override
  Future<void> onLoad() async {
    // Add background
    add(RectangleComponent(
      size: size,
      paint: Paint()..color = const Color(0xFF001122),
    ));

    // Create bricks (small circles at top)
    createBricks();

    // Add ball
    ball = Ball();
    add(ball);

    // Add paddle
    paddle = Paddle();
    add(paddle);

    // Add score text
    scoreText = TextComponent(
      text: 'Score: 0',
      position: Vector2(20, 50),
      textRenderer: TextPaint(
        style: const TextStyle(
          fontSize: 24,
          color: Colors.white,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
    add(scoreText);

    // Add game over text (initially hidden)
    gameOverText = TextComponent(
      text: 'Game Over!\nTap to Restart',
      position: Vector2(size.x / 2, size.y / 2),
      anchor: Anchor.center,
      textRenderer: TextPaint(
        style: const TextStyle(
          fontSize: 32,
          color: Colors.red,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
    gameOverText.scale = Vector2.zero();
    add(gameOverText);

    // Add win text (initially hidden)
    winText = TextComponent(
      text: 'You Win!\nTap to Restart',
      position: Vector2(size.x / 2, size.y / 2),
      anchor: Anchor.center,
      textRenderer: TextPaint(
        style: const TextStyle(
          fontSize: 32,
          color: Colors.green,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
    winText.scale = Vector2.zero();
    add(winText);
  }

  void createBricks() {
    const rows = 5;
    const cols = 8;
    const brickSize = 40.0;
    const spacing = 60.0;
    const startY = 100.0;

    for (int row = 0; row < rows; row++) {
      for (int col = 0; col < cols; col++) {
        final brick = Brick();
        brick.position = Vector2(
          (col + 1) * spacing,
          startY + row * spacing,
        );
        bricks.add(brick);
        add(brick);
      }
    }
  }

  void breakBrick(Brick brick) {
    brick.removeFromParent();
    bricks.remove(brick);
    score += 10;
    scoreText.text = 'Score: $score';
    
    // Check win condition
    if (bricks.isEmpty) {
      isWin = true;
      winText.scale = Vector2.all(1);
    }
  }

  void gameOver() {
    isGameOver = true;
    gameOverText.scale = Vector2.all(1);
  }

  void restartGame() {
    isGameOver = false;
    isWin = false;
    score = 0;
    scoreText.text = 'Score: 0';
    gameOverText.scale = Vector2.zero();
    winText.scale = Vector2.zero();
    
    // Remove all existing bricks
    for (final brick in bricks) {
      brick.removeFromParent();
    }
    bricks.clear();
    
    // Create new bricks
    createBricks();
    
    // Reset ball position and velocity
    ball.position = Vector2(size.x / 2, size.y / 2);
    ball.velocity = Vector2(200, -200);
    
    // Reset paddle position
    paddle.position = Vector2(size.x / 2 - paddle.size.x / 2, size.y - 50);
  }

  @override
  bool onTapDown(TapDownInfo info) {
    if (isGameOver || isWin) {
      restartGame();
    } else {
      // Move paddle towards tap
      final tapX = info.eventPosition.global.x;
      paddle.moveTo(tapX);
    }
    return true;
  }
}

class Ball extends RectangleComponent with HasGameRef<BreakoutGame>, CollisionCallbacks {
  Vector2 velocity = Vector2(200, -200);
  
  @override
  Future<void> onLoad() async {
    size = Vector2.all(20);
    anchor = Anchor.center;
    position = Vector2(game.size.x / 2, game.size.y / 2);
    paint = Paint()..color = Colors.white;
    
    add(RectangleHitbox());
  }

  @override
  void render(Canvas canvas) {
    // Draw as circle
    canvas.drawCircle(
      Offset(size.x / 2, size.y / 2),
      size.x / 2,
      paint,
    );
  }

  @override
  void update(double dt) {
    super.update(dt);
    
    if (game.isGameOver || game.isWin) return;
    
    position += velocity * dt;
    
    // Bounce off walls
    if (position.x <= size.x / 2 || position.x >= game.size.x - size.x / 2) {
      velocity.x = -velocity.x;
      position.x = position.x.clamp(size.x / 2, game.size.x - size.x / 2);
    }
    
    if (position.y <= size.y / 2) {
      velocity.y = -velocity.y;
      position.y = size.y / 2;
    }
    
    // Game over if ball goes below paddle
    if (position.y > game.size.y) {
      game.gameOver();
    }
  }

  @override
  bool onCollisionStart(Set<Vector2> intersectionPoints, PositionComponent other) {
    if (other is Brick) {
      velocity.y = -velocity.y;
      game.breakBrick(other);
      return false;
    } else if (other is Paddle) {
      // Calculate bounce angle based on where ball hits paddle
      final paddleCenter = other.position.x + other.size.x / 2;
      final hitOffset = (position.x - paddleCenter) / (other.size.x / 2);
      
      velocity.y = -velocity.y.abs(); // Always bounce up
      velocity.x = hitOffset * 300; // Vary horizontal speed based on hit position
      
      return false;
    }
    return true;
  }
}

class Paddle extends RectangleComponent with HasGameRef<BreakoutGame> {
  double targetX = 0;
  double moveSpeed = 500;

  @override
  Future<void> onLoad() async {
    size = Vector2(100, 15);
    anchor = Anchor.topLeft;
    position = Vector2(game.size.x / 2 - size.x / 2, game.size.y - 50);
    paint = Paint()..color = Colors.blue;
    
    add(RectangleHitbox());
    targetX = position.x;
  }

  @override
  void update(double dt) {
    super.update(dt);
    
    // Move towards target X position
    final diff = targetX - position.x;
    if (diff.abs() > 5) {
      final moveX = moveSpeed * dt * (diff > 0 ? 1 : -1);
      position.x += moveX;
      
      // Keep paddle within screen bounds
      position.x = position.x.clamp(0, game.size.x - size.x);
    }
  }

  void moveTo(double x) {
    targetX = (x - size.x / 2).clamp(0, game.size.x - size.x);
  }
}

class Brick extends RectangleComponent with HasGameRef<BreakoutGame> {
  @override
  Future<void> onLoad() async {
    size = Vector2.all(40);
    anchor = Anchor.center;
    paint = Paint()..color = Colors.orange;
    
    add(RectangleHitbox());
  }

  @override
  void render(Canvas canvas) {
    // Draw as circle
    canvas.drawCircle(
      Offset(size.x / 2, size.y / 2),
      size.x / 2,
      paint,
    );
  }
}
